import { queryShOptionAppointmentInfo } from '@/service/service';

/**
 * 业务预处理器 - 负责在流程初始化前进行业务相关的预处理
 * 采用策略模式，支持不同业务类型的扩展
 */
class BusinessPreProcessor {
  constructor() {
    this.strategies = new Map();
  }

  /**
   * 注册业务策略
   * @param {string} bizType - 业务类型
   * @param {Object} strategy - 策略实例
   */
  registerStrategy(bizType, strategy) {
    this.strategies.set(bizType, strategy);
  }

  /**
   * 处理业务预处理逻辑
   * @param {string} bizType - 业务类型
   * @param {Object} context - 上下文对象
   * @returns {Promise<{shouldContinue: boolean}>}
   */
  async process(bizType, context) {
    const strategy = this.strategies.get(bizType);
    if (strategy) {
      return await strategy.execute(context);
    }
    // 默认策略：继续执行流程
    return { shouldContinue: true };
  }
}

/**
 * 期权账户策略 - 处理期权账户相关的预处理逻辑
 */
class OptionAccountStrategy {
  /**
   * 执行期权账户预处理逻辑
   * @param {Object} context - 上下文对象，包含Vue实例等
   * @returns {Promise<{shouldContinue: boolean}>}
   */
  async execute(context) {
    try {
      const response = await queryShOptionAppointmentInfo();

      if (response?.data?.flowInsId) {
        // 需要跳转到期权账户结果页
        return this._handleJumpToOptionResult(context);
      }

      // 继续正常流程
      return { shouldContinue: true };
    } catch (error) {
      console.error('查询期权预约信息失败:', error);
      // 出错时继续正常流程，避免阻塞用户操作
      return { shouldContinue: true };
    }
  }

  /**
   * 处理跳转到期权账户结果页的逻辑
   * @param {Object} context - 上下文对象
   * @returns {{shouldContinue: boolean}}
   * @private
   */
  _handleJumpToOptionResult(context) {
    try {
      // 跳转到期权账户结果页
      context.$router.replace({
        name: 'optionAccountResult'
      });
      return { shouldContinue: false };
    } catch (error) {
      console.error('跳转到期权账户结果页失败:', error);
      return { shouldContinue: true };
    }
  }
}

// 创建全局实例并注册策略
const processor = new BusinessPreProcessor();
processor.registerStrategy('010169', new OptionAccountStrategy());

export default processor;
