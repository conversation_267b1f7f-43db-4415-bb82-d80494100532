var config = {
  targetUrl: serviceOptions.target,
  loginHostUrl: 'webapps.yongjinbao.com.cn', //H5统一登录唤起页面
  serverUrl: serviceOptions.server + '/bc-bff-server',
  fileUrl: serviceOptions.server + '/bc-bff-server/file',
  commonServerUrl: serviceOptions.server + '/work-common-server',
  pdfFileUrl: serviceOptions.server + '/file-common-server/file', // 协议pdf文件服务路径
  tKFlowConfig: {
    serverUrl: serviceOptions.server,
    isDebug: 1,
    requestMode: 2, //请求模式0:不加密，不加签，1：加签不加密，2：加密加签, 3:加密加签,响应头也加密
    merchantKey: '',
    merchantSecret: ''
  },
  moduleName: 'ygt',
  showHome: serviceOptions.dev,
  flowVersion: '2.0',
  opEntrustWay: '7',
  themeColor: '#fa443a',
  mainColor: '#fa443a',
  merchantId: '0', // 商户ID
  needNativeUpload: false,
  thirdPartyUrl: {
    index:
      serviceOptions.target + '/yjbwebmoc/moc/web/moc-pro/build/indexView.html',
    openOptionAccount:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=optionAccountForWeb', //开通股票期权
    basicInfo:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=personalForWeb', //基本信息完善
    businessDepartment:
      'http://www.yongjinbao.com.cn/mobile/about.html?tag=dept&#dept', // 查找营业部
    highFinancial:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=highFinancialForWeb', // 高端理财
    retrieveAccount:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc/findaccount/personinfo.html', //找回账号
    reissueStockAcc:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb', // 补开股东户
    kcbIntroduction:
      serviceOptions.target +
      '/yjbwebstockLiving/stockLiving/web/stockLiving/build/html/videoAlbum.html?id=1000037&groupNo=', //科创版及相关规则介绍
    rzrqIntroduction:
      serviceOptions.target +
      '/yjbwebstockLiving/stockLiving/web/stockLiving/build/html/videoAlbum.html?id=1000039&groupNo=', //融资融券业务知识及风险提示
    bjhgFinancialList: serviceOptions.financialServer, // 报价回购理财页面
    creditStockAcc:
      serviceOptions.target +
      '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=creditAForWeb' // 开信用股东户
  },
  //思迪研发业务
  tkBizTypeList: [
    {
      bizType: '010044', // 补开股东户
      pageName: 'addShareHoldAccount'
    },
    {
      bizType: '010129' //开通股票期权
    }
  ],
  video: {
    tchat: {
      signalServer: 'wss://tchat.lczq.com:9001/',
      tchat_verifier_secret_key: '123456', //信令服务器认证秘钥
      protobufFileLoaction: '/bc-h5-view/views/tchat/sd.data_structure.proto'
    },
    videoServer: serviceOptions.server, // 集中视频地址
    videoType: '4', // 0tchat 1anychat
    anychat: {
      appId: ''
    },
    liveFailNum: 3, // app活体识别失败跳转双向次数配置
    liveFaceFailNum: 10, // 活体在框检测错误次数跳转错误提示页面
    liveFaceTotalFileNum: 3, // 活体在框检测错误跳转错误提示页面次数引导双向
    liveActionFailNum: 3, // 活体动作检测失败次数跳转错误提示页面
    liveActionTotalFailNum: 3, // 活体动作检测失败跳转错误提示页面次数引导双向
    faceFailNum: 3, // 人脸对比错误最大次数
    recordFaceFailNum: 10, // 视频录制在框检测错误次数
    recordFaceTotalFailNum: 3, // 视频录制在框检测跳转错误提示页面引导双向
    recordFaceCompareFailNum: 3, // 视频录制人脸对比错误次数
    recordFaceCompareTotalFailNum: 3, // 视频录制人脸对比跳转错误提示页面次数引导双向
    recordReplayFailNum: 3, // 语音播报版本视频录制回答错误次数
    recordNoSoundFailNum: 3, // 语音播报版本视频录制回答无声音错误次数
    checkIntvTime: 1000, // 在框检测间隔时间  单位ms
    recordMaxTime: 30 // 视频录制最长时限 单位s
  },
  protocolReadSeconds: '5', // 协议强制阅读秒数（单位秒） 传空为不强制阅读
  sendSMSCount: '60', // 短信验证码倒计时
  passwordEditorOFF: true, // 是否关闭安全控件(默认开启,仅本地环境配置,生产环境无需配置) true关闭控件 false开启控件
  safeControlName: 'SafeLoginSetup.zip', // 安全控件的名字以及文件类型定义
  thirdPartyChannels: ['compass'], // 配置第三方渠道标识
  // 跳转url以及公共编号
  businessPath: [
    // 账户权限视图
    {
      bizType: 'account',
      gjzqBizUrl: '',
      announcementNo: '',
      routName: 'accountPermissionAttempt'
    },
    // 身份证更新
    {
      bizType: '010006',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=idupdateForWeb',
      announcementNo: '********',
      routName: ''
    },
    // 风险测评
    {
      bizType: '010013',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb',
      announcementNo: '********',
      routName: '',
      routquery: ''
    },
    // 修改个人资料
    {
      bizType: '010004',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=personalForWeb',
      announcementNo: '********',
      routName: 'clientInfoModify',
      routquery: ''
    },
    // 补开股东户
    {
      bizType: '010044',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockAForWeb',
      announcementNo: '********',
      routName: 'addShareHoldAccount',
      routquery: ''
    },
    // 指定交易
    {
      bizType: '010053',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=specifiedForWeb',
      announcementNo: '********',
      routName: '',
      routquery: ''
    },
    // 销户
    {
      bizType: '010731',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=reservationListForWeb',
      announcementNo: '********',
      routName: 'businessIntroduce',
      routquery: { bizType: '010731' }
    },
    // 创业板
    {
      bizType: '010061',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=startUpBoardV2QueryForWeb',
      announcementNo: '********',
      routName: 'businessIntroduce',
      routquery: { bizType: '010061' }
    },
    // 信用创业板
    {
      bizType: '010245',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=creditStartUpBoardQueryForWeb',
      announcementNo: '10020043',
      routName: 'businessIntroduce',
      routquery: { bizType: '010245' }
    },
    // 两融预约开户
    {
      bizType: '010174',
      gjzqBizUrl:
        serviceOptions.financingFacilityServer +
        '/yjbwxkh/openstock/web/reserveMargin.v4.html?app_id=${appId}&channel_type=1009000000001&activity_no=1000220870',
      announcementNo: '',
      routName: 'reserveMargin'
    },
    // 北交所权限开通
    {
      bizType: '010180',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=stockBeiQueryForWeb',
      announcementNo: '',
      routName: 'businessIntroduce',
      routquery: { bizType: '010180' }
    },
    // 信用北交所权限开通
    {
      bizType: '010221',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=creditStockBeiQueryForWeb',
      announcementNo: '',
      routName: 'businessIntroduce',
      routquery: { bizType: '010221' }
    },
    // 关联关系确认
    {
      bizType: '010197',
      gjzqBizUrl:
        serviceOptions.target +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=associationConfirmForWeb',
      announcementNo: '',
      routName: 'businessIntroduce',
      routquery: { bizType: '010197' }
    },
    // 开通股票期权全真模拟交易账户
    {
      bizType: '010135',
      routName: '',
      announcementNo: '********'
    },
    // 预约沪市期权开户
    {
      bizType: '010169',
      routName: '',
      announcementNo: '********'
    },
    // 可转债权限开通
    {
      bizType: '010172',
      gjzqBizUrl: '',
      announcementNo: '',
      routName: 'businessIntroduce',
      routquery: { bizType: '010172' }
    },
    // 新增信用三方存管
    {
      bizType: '010294',
      gjzqBizUrl: '',
      announcementNo: '',
      routName: 'creditThirdpartAccountList'
    },
    // 变更信用三方存管
    {
      bizType: '010259',
      gjzqBizUrl: '',
      announcementNo: '',
      routName: 'creditThirdpartAccountList'
    },
    // 管理信用额度
    {
      bizType: '010098',
      gjzqBizUrl: '',
      announcementNo: '',
      routName: 'showCreditQuota'
    },
     // 开通科创板成长层
    {
      bizType: '014004',
      gjzqBizUrl: '',
      announcementNo: '********',
      routName: 'businessIntroduce',
      routquery: { bizType: '014004' }
    },
     // 科创板交易权限申请
    {
      bizType: '010086',
      gjzqBizUrl: '',
      announcementNo: '********',
      routName: 'businessIntroduce',
      routquery: { bizType: '010086' }
    }
  ],
  // 结果页banner图配置
  bannerConfig: [
    {
      bizType: '010254', //报价回购开通
      imageName: 'result_banner',
      url: serviceOptions.financialServer,
      getToken: true //是否需要生成token跳转
    },
    {
      bizType: '010086', //科创版开通
      imageName: 'result_banner_kcb',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=A0169A0D27C727246047AFFCD6BEED96'
    },
    {
      bizType: '010061', //创业板开通
      imageName: 'result_banner_cyb',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=5C0F89E0B86F364E11922F5BC963E699'
    },
    {
      bizType: '010117', //新三板开通
      imageName: 'result_banner_xsb',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=9EE1086576D774D3A091FD18D75F98E0'
    },
    {
      bizType: '010080', //港股通开通
      imageName: 'result_banner_ggt',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=53732795AB70CF1459AA90C2117A21FD'
    },
    {
      bizType: '010129', //期权开户
      imageName: 'result_banner_qqkh',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=9EEFD86D2F5F876AB714DF4E5DD11FF3'
    },
    {
      bizType: '010252', //场内基金账户开户
      imageName: 'result_banner_cnjj',
      url: 'https://webapps.yongjinbao.com.cn/ims/content-view/view?contentId=62426887BF054DCD0A2832496EFDEF3E'
    },
    {
      bizType: '010180', // 北交所权限
      imageName: 'result_banner_ktbjs',
      url: 'https://g.gjyjb.com/rd/Wz7qVy'
    },
    {
      bizType: '010221', // 信用北交所权限
      imageName: 'result_banner_ktbjs',
      url: 'https://g.gjyjb.com/rd/Wz7qVy'
    },
    {
      bizType: '014004', // 开通科创板成长层权限
      imageName: 'result_banner_kcb',
      url: 'https://open.work.weixin.qq.com/wwopen/mpnews?mixuin=rgSmEQAABwBewWG9AAAUAA&mfid=WW0308-KRKkvwAABwChd4TKgQlwrQcuBI894&idx=0&sn=e15adb1afa2abb2f804fc6a02a6bf41d&version=4.1.38.6006&platform=win'
    }
  ]
};

window.$hvue = window.$hvue ? window.$hvue : {};
window.$hvue.customConfig = config;
